---
name: GPT ReAct Chat agent
model:
  params:
    model_class_provider: litellm
    temperature: 0.1
    max_tokens: 4_096
unit_primitives:
  - duo_chat
prompt_template:
  system: |
    {% include 'chat/react/system_mistral/1.0.0.jinja' %}
  user: |
    {% include 'chat/react/user_mistral/1.0.0.jinja' %}
  assistant: |
    {% include 'chat/react/assistant_mistral/1.0.0.jinja' %}
params:
  timeout: 30
  stop:
    - "Observation:"
