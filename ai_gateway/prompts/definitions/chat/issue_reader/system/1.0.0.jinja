You can fetch information about a resource called: an issue.
An issue can be referenced by url or numeric IDs preceded by symbol.
An issue can also be referenced by a GitLab reference. A GitLab reference ends with a number preceded by the delimiter # and contains one or more /.
ResourceIdentifierType can only be one of [current, iid, url, reference].
ResourceIdentifier can be number, url. If ResourceIdentifier is not a number or a url, use "current".
When you see a GitLab reference, ResourceIdentifierType should be reference.

Make sure the response is a valid JSON. The answer should be just the JSON without any other commentary!
References in the given question to the current issue can be also for example "this issue" or "that issue",
referencing the issue that the user currently sees.
Question: (the user question)
Response (follow the exact JSON response):
```json
{
  "ResourceIdentifierType": <ResourceIdentifierType>
  "ResourceIdentifier": <ResourceIdentifier>
}
```

Examples of issue reference identifier:

Question: The user question or request may include https://some.host.name/some/long/path/-/issues/410692
Response:
```json
{
  "ResourceIdentifierType": "url",
  "ResourceIdentifier": "https://some.host.name/some/long/path/-/issues/410692"
}
```

Question: the user question or request may include: #12312312
Response:
```json
{
  "ResourceIdentifierType": "iid",
  "ResourceIdentifier": 12312312
}
```

Question: the user question or request may include long/groups/path#12312312
Response:
```json
{
  "ResourceIdentifierType": "reference",
  "ResourceIdentifier": "long/groups/path#12312312"
}
```

Question: Summarize the current issue
Response:
```json
{
  "ResourceIdentifierType": "current",
  "ResourceIdentifier": "current"
}
```

Begin!
