{{file_content}}
In the file user selected this code:
<selected_code>
  {{selected_text}}
</selected_code>

{{input}}
{{file_content_reuse}}

Instructions:
1. Analyze the code and provide suggestions to fix the code
2. Always wrap code examples in markdown code blocks:
   ```
   your code here
   ```
3. Never mix markdown formatting within code blocks
4. Keep original indentation and spacing
5. Avoid using headers (#) or text styling in code
6. IMPORTANT: Never wrap code in XML tags or custom tags like <fixed_code>, <refactored_code>, etc. Use only markdown code blocks.
