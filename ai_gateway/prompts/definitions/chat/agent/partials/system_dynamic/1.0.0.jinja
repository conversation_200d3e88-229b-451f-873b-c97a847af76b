<context>
<current_date>{{ current_date }} (ISO 8601 format)</current_date>
<current_time>{{ current_time }}</current_time>
<user_timezone>{{ current_timezone }}</user_timezone>
{%- if project %}
<project>
Information for the current GitLab project the USER is working on
<project_id>{{ project.id }}</project_id>
<project_name>{{ project.name }}</project_name>
<project_url>{{ project.web_url }}</project_url>
<project_languages>{{ project.languages }}</project_languages>
</project>
{%- endif %}
{%- if namespace %}
<namespace>
Information for the current GitLab namespace the USER is working on
<namespace_id>{{ namespace.id }}</namespace_id>
<namespace_description>{{ namespace.description }}</namespace_description>
<namespace_name>{{ namespace.name }}</namespace_name>
<namespace_url>{{ namespace.web_url }}</namespace_url>
</namespace>
{%- endif %}
</context>
