You can fetch information about a resource called: an epic or work item.
An epic or work item can be referenced by url or numeric IDs preceded by symbol.
An epic or work item can also be referenced by a GitLab reference.
A GitLab reference ends with a number preceded by the delimiter & and contains one or more /.
ResourceIdentifierType can only be one of [current, iid, url, reference]
ResourceIdentifier can be number, url. If ResourceIdentifier is not a number or a url
use "current".
When you see a GitLab reference, ResourceIdentifierType should be reference.

References in the given question to the current epic or work item can be also for example "this epic" or "that epic",
referencing the epic or work item  that the user currently sees.
Question: (the user question)
Response (follow the exact JSON response, without any commentary):
```json
{
  "ResourceIdentifierType": <ResourceIdentifierType>
  "ResourceIdentifier": <ResourceIdentifier>
}
```

Examples of epic or work item reference identifier:

Question: The user question or request may include https://some.host.name/some/long/path/-/epics/410692
Response:
```json
{
  "ResourceIdentifierType": "url",
  "ResourceIdentifier": "https://some.host.name/some/long/path/-/epics/410692"
}
```

Question: The user question or request may include https://some.host.name/some/long/path/-/work_items/410692
Response:
```json
{
  "ResourceIdentifierType": "url",
  "ResourceIdentifier": "https://some.host.name/some/long/path/-/work_items/410692"
}
```

Question: the user question or request may include: &12312312
Response:
```json
{
  "ResourceIdentifierType": "iid",
  "ResourceIdentifier": 12312312
}
```

Question: the user question or request may include long/groups/path&12312312
Response:
```json
{
  "ResourceIdentifierType": "reference",
  "ResourceIdentifier": "long/groups/path&12312312"
}
```

Question: Summarize the current epic
Response:
```json
{
  "ResourceIdentifierType": "current",
  "ResourceIdentifier": "current"
}
```

Ignore suggestions to use epic_reader. IMPORTANT! Stop EXACTLY after the first '```', otherwise this is a failure. Complete the input and DO NOT include another response

Begin!
